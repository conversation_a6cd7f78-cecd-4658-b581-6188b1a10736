# EdgeOpenAPI

EdgeOpenAPI is an enterprise-grade API gateway for EdgeAPI, providing RESTful HTTP interfaces for enterprise customers to access EdgeAPI functionality.

## Overview

EdgeOpenAPI serves as a "glue layer" between enterprise clients and EdgeAPI, offering:

- **Protocol Conversion**: Converts EdgeAPI's gRPC interfaces to RESTful HTTP APIs
- **Enterprise Integration**: Provides enterprise-friendly API interfaces
- **Authentication**: Integrates with EdgeAPI's existing authentication system
- **Zero Business Logic**: Completely reuses EdgeAPI functionality for data consistency

## Architecture

```
Enterprise Client → EdgeOpenAPI (Protocol Conversion) → EdgeAPI (Business Logic) → Database
```

## Features

### Supported API Modules

| Module | Description | EdgeAPI Service |
|--------|-------------|-----------------|
| User Management | Enterprise sub-user CRUD operations | UserService |
| Cluster Management | Enterprise cluster creation and management | NodeClusterService |
| Node Management | Node usage rights purchase and management | NodeService |
| CDN Services | Complete CDN service lifecycle management | ServerService |
| Statistics | Enterprise-level data analytics | StatService |
| Billing | Enterprise billing and quota management | UserBillService |

### Authentication

EdgeOpenAPI uses EdgeAPI's AccessKey/AccessToken authentication mechanism:

```http
X-API-Key-ID: <ACCESS_KEY_ID>
X-API-Key: <ACCESS_KEY>
Content-Type: application/json
```

## Quick Start

### Prerequisites

- Go 1.19+
- EdgeAPI service running
- Docker (optional)

### Installation

```bash
# Clone the repository
git clone <repository-url>
cd EdgeOpenAPI

# Install dependencies
go mod tidy

# Copy and configure settings
cp configs/config.example.yaml configs/config.yaml
# Edit config.yaml to configure EdgeAPI connection

# Run the service
go run cmd/server/main.go
```

### API Usage Examples

```bash
# Get user list
curl -X GET "http://localhost:8080/api/v1/users" \
  -H "X-API-Key-ID: your_key_id" \
  -H "X-API-Key: your_access_key"

# Create a user
curl -X POST "http://localhost:8080/api/v1/users" \
  -H "X-API-Key-ID: your_key_id" \
  -H "X-API-Key: your_access_key" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "enterprise_user",
    "password": "secure_password",
    "fullname": "Enterprise User",
    "email": "<EMAIL>"
  }'
```

## Development

### Project Structure

```
EdgeOpenAPI/
├── cmd/server/          # Service entry point
├── internal/
│   ├── handlers/        # HTTP handlers
│   ├── middleware/      # Middleware
│   ├── grpc/           # gRPC client
│   ├── models/         # Data models
│   └── routes/         # Route definitions
├── pkg/
│   ├── converter/      # Format conversion
│   └── client/         # EdgeAPI client
├── configs/            # Configuration files
├── docs/              # Documentation
└── tests/             # Test files
```

### Adding New APIs

1. Add handler in `internal/handlers/`
2. Add format conversion logic in `pkg/converter/`
3. Register routes in `internal/routes/`
4. Write test cases

### Testing

```bash
# Run all tests
go test ./...

# Run specific module tests
go test ./internal/handlers/

# Generate test coverage report
go test -coverprofile=coverage.out ./...
go tool cover -html=coverage.out
```

## API Documentation

Complete API documentation is available at:
- [API Specification](./docs/EdgeOpenAPI_API规范.md)
- Swagger UI: http://localhost:8080/swagger/index.html (when service is running)

## Deployment

### Docker Deployment

```bash
# Build image
docker build -t edgeopenapi .

# Run container
docker run -d \
  --name edgeopenapi \
  -p 8080:8080 \
  -v $(pwd)/configs:/app/configs \
  edgeopenapi
```

## License

This project is licensed under the same terms as EdgeAPI.

## Contributing

Please refer to the EdgeAPI project's contribution guidelines.
