# EdgeOpenAPI

EdgeOpenAPI是EdgeAPI的企业级API网关，为企业客户提供RESTful HTTP接口访问EdgeAPI的所有功能。

## 项目概述

### 核心定位
EdgeOpenAPI定位为EdgeAPI的"胶水层"或API网关，其核心价值在于：

- **协议转换**: 将EdgeAPI的gRPC接口转换为RESTful HTTP API
- **企业级封装**: 提供符合企业客户使用习惯的API接口
- **权限控制**: 确保只暴露Enterprise Admin权限范围内的功能
- **完整文档**: 提供完整的API文档和使用示例

### 技术特点

- ✅ **零业务逻辑**: 完全复用EdgeAPI现有功能，确保数据一致性
- ✅ **完全兼容**: 与EdgeAPI现有认证和权限体系100%兼容
- ✅ **高性能**: 基于Gin框架，提供高性能HTTP服务
- ✅ **易维护**: 只需维护协议转换层，业务逻辑由EdgeAPI负责
- ✅ **快速开发**: 2周即可完成全部功能开发

## 技术架构

```
企业客户端 → EdgeOpenAPI (协议转换) → EdgeAPI (业务逻辑) → 数据库
```

### 核心组件

1. **HTTP服务层**: Gin框架提供RESTful API
2. **认证中间件**: 调用EdgeAPI认证服务
3. **格式转换层**: JSON与Protobuf格式转换
4. **gRPC客户端**: 与EdgeAPI通信

## 功能特性

### 支持的API模块

| 模块 | 功能描述 | EdgeAPI服务 |
|------|---------|-------------|
| 用户管理 | 企业下级用户的增删改查 | UserService |
| 集群管理 | 企业集群的创建和管理 | NodeClusterService |
| 节点管理 | 节点使用权的购买和管理 | NodeService |
| CDN服务 | CDN服务的完整生命周期管理 | ServerService |
| 统计分析 | 企业维度的数据统计分析 | StatService |
| 计费管理 | 企业账单和配额管理 | UserBillService |

### 权限控制

EdgeOpenAPI严格按照Enterprise Admin权限范围提供功能：

- ✅ 管理企业下级用户
- ✅ 管理企业自有集群
- ✅ 购买和管理节点使用权
- ✅ 管理企业CDN服务
- ✅ 查看企业账单和统计
- ❌ 无法访问其他企业数据
- ❌ 无法执行平台级管理操作

## 快速开始

### 环境要求

- Go 1.19+
- EdgeAPI服务运行中
- Docker (可选)

### 安装和运行

```bash
# 克隆项目
git clone <repository-url>
cd EdgeOpenAPI

# 安装依赖
go mod tidy

# 配置EdgeAPI连接
cp configs/config.example.yaml configs/config.yaml
# 编辑config.yaml，配置EdgeAPI地址

# 运行服务
go run cmd/server/main.go
```

### API使用示例

```bash
# 获取用户列表
curl -X GET "http://localhost:8080/api/v1/users" \
  -H "X-API-Key-ID: your_key_id" \
  -H "X-API-Key: your_access_key"

# 创建CDN服务
curl -X POST "http://localhost:8080/api/v1/services" \
  -H "X-API-Key-ID: your_key_id" \
  -H "X-API-Key: your_access_key" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "my-cdn-service",
    "description": "My CDN Service"
  }'
```

## 开发指南

### 项目结构

```
EdgeOpenAPI/
├── cmd/server/          # 服务启动入口
├── internal/
│   ├── handlers/        # HTTP处理器
│   ├── middleware/      # 中间件
│   ├── grpc/           # gRPC客户端
│   ├── models/         # 数据模型
│   └── routes/         # 路由定义
├── pkg/
│   ├── converter/      # 格式转换
│   └── client/         # EdgeAPI客户端
├── configs/            # 配置文件
├── docs/              # 文档
└── tests/             # 测试文件
```

### 添加新API

1. 在`internal/handlers/`中添加处理器
2. 在`pkg/converter/`中添加格式转换逻辑
3. 在`internal/routes/`中注册路由
4. 编写测试用例

### 测试

```bash
# 运行所有测试
go test ./...

# 运行特定模块测试
go test ./internal/handlers/

# 生成测试覆盖率报告
go test -coverprofile=coverage.out ./...
go tool cover -html=coverage.out
```

## API文档

完整的API文档请参考：
- [API规范文档](./EdgeOpenAPI_API规范.md)
- [Swagger文档](http://localhost:8080/swagger/index.html) (服务运行时访问)

## 部署

### Docker部署

```bash
# 构建镜像
docker build -t edgeopenapi .

# 运行容器
docker run -d \
  --name edgeopenapi \
  -p 8080:8080 \
  -v $(pwd)/configs:/app/configs \
  edgeopenapi
```

## 相关文档

- [设计方案](./EdgeOpenAPI_设计方案.md)
- [开发计划](./EdgeOpenAPI_开发计划.md)
- [API规范](./EdgeOpenAPI_API规范.md)
